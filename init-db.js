/**
 * Database Initialization Script
 * Creates SQLite database and tables for Jalali Calendar application
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// ============================================================================
// DATABASE CONFIGURATION
// ============================================================================
const dbPath = path.join(__dirname, 'calendar.db');
const db = new sqlite3.Database(dbPath);

console.log('Initializing database...');

// ============================================================================
// DATABASE SCHEMA CREATION
// ============================================================================
db.serialize(() => {
    // Create events table
    db.run(`CREATE TABLE IF NOT EXISTS events (
        id TEXT PRIMARY KEY,
        jalali_date TEXT NOT NULL,
        title TEXT NOT NULL,
        time TEXT,
        tag TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`, (err) => {
        if (err) {
            console.error('Error creating events table:', err);
        } else {
            console.log('Events table created successfully');
        }
    });

    // Create index on jalali_date for faster queries
    db.run(`CREATE INDEX IF NOT EXISTS idx_jalali_date ON events(jalali_date)`, (err) => {
        if (err) {
            console.error('Error creating index:', err);
        } else {
            console.log('Index created successfully');
        }
    });

    // Create trigger to update updated_at timestamp
    db.run(`CREATE TRIGGER IF NOT EXISTS update_events_timestamp 
            AFTER UPDATE ON events 
            BEGIN 
                UPDATE events SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
            END`, (err) => {
        if (err) {
            console.error('Error creating trigger:', err);
        } else {
            console.log('Trigger created successfully');
        }
    });
});

// ============================================================================
// DATABASE CLOSURE
// ============================================================================
db.close((err) => {
    if (err) {
        console.error('Error closing database:', err);
    } else {
        console.log('Database initialized successfully!');
        console.log('Database file created at:', dbPath);
    }
});
