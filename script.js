
const Jalaali = (() => {
    function div(a, b) { return ~~(a / b); }
    function mod(a, b) { return a - b * div(a, b); }
    
    function jalCal(jy) {
        const breaks = [-61, 9, 38, 199, 426, 686, 756, 818, 1111, 1181, 1210, 1635, 2060, 2097, 2192, 2262, 2324, 2394, 2456, 3178];
        let bl = breaks.length, gy = jy + 621, leapJ = -14, jp = breaks[0], jm, jump, leap, n, i;
        
        if (jy < jp || jy >= breaks[bl - 1]) throw new Error('jalCal: out of range');
        
        for (i = 1; i < bl; i++) {
            jm = breaks[i];
            jump = jm - jp;
            if (jy < jm) break;
            leapJ += div(jump, 33) * 8 + div(mod(jump, 33), 4);
            jp = jm;
        }
        
        n = jy - jp;
        leapJ += div(n, 33) * 8 + div(mod(n, 33) + 3, 4);
        if (mod(jump, 33) === 4 && jump - n === 4) leapJ++;
        
        let leapG = div(gy, 4) - div((div(gy, 100) + 1) * 3, 4) - 150;
        let march = 20 + leapJ - leapG;
        
        if (jump - n < 6) n = n - jump + div(jump + 4, 33) * 33;
        leap = mod(mod(n + 1, 33) - 1, 4);
        if (leap === -1) leap = 4;
        
        return { leap: leap === 0, gy, march };
    }
    
    function g2d(gy, gm, gd) {
        let a = div(14 - gm, 12);
        let y = gy + 4800 - a;
        let m = gm + 12 * a - 3;
        return gd + div(153 * m + 2, 5) + 365 * y + div(y, 4) - div(y, 100) + div(y, 400) - 32045;
    }
    
    function d2g(jdn) {
        let j = jdn + 32044;
        let g = div(j, 146097);
        let dg = mod(j, 146097);
        let c = div((div(dg, 36524) + 1) * 3, 4);
        let dc = dg - c * 36524;
        let b = div(dc, 1461);
        let db = mod(dc, 1461);
        let a = div((div(db, 365) + 1) * 3, 4);
        let da = db - a * 365;
        let y = g * 400 + c * 100 + b * 4 + a;
        let m = div(5 * da + 308, 153) - 2;
        let d = da - div(153 * m + 2, 5) + 1;
        
        y = y - 4800 + div(m + 2, 12);
        m = mod(m + 2, 12) + 1;
        
        return { gy: y, gm: m, gd: d };
    }
    
    function j2d(jy, jm, jd) {
        const r = jalCal(jy);
        return g2d(r.gy, 3, r.march) + (jm - 1) * 31 - div(jm, 7) * (jm - 7) + jd - 1;
    }
    
    function d2j(jdn) {
        const g = d2g(jdn);
        let jy = g.gy - 621;
        const r = jalCal(jy);
        let jdn1 = g2d(r.gy, 3, r.march);
        let k = jdn - jdn1;
        
        if (k >= 0) {
            if (k <= 185) {
                const jm = 1 + div(k, 31);
                const jd = mod(k, 31) + 1;
                return { jy, jm, jd };
            }
            k -= 186;
            return { jy, jm: 7 + div(k, 30), jd: mod(k, 30) + 1 };
        }
        
        jy -= 1;
        const r2 = jalCal(jy);
        jdn1 = g2d(r2.gy, 3, r2.march);
        k = jdn - jdn1;
        
        if (k <= 185) {
            return { jy, jm: 1 + div(k, 31), jd: mod(k, 31) + 1 };
        }
        k -= 186;
        return { jy, jm: 7 + div(k, 30), jd: mod(k, 30) + 1 };
    }
    
    function toJ(gy, gm, gd) { return d2j(g2d(gy, gm, gd)); }
    function toG(jy, jm, jd) { return d2g(j2d(jy, jm, jd)); }
    function monthLength(jy, jm) { return jm <= 6 ? 31 : (jm <= 11 ? 30 : (isLeap(jy) ? 30 : 29)); }
    function isLeap(jy) { return jalCal(jy).leap; }
    
    return { toJ, toG, monthLength, isLeap, j2d, d2j };
})();


const WEEKDAYS = ["شنبه", "یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنجشنبه", "جمعه"];
const MONTHS = ["فروردین", "اردیبهشت", "خرداد", "تیر", "مرداد", "شهریور", "مهر", "آبان", "آذر", "دی", "بهمن", "اسفند"];


const state = { 
    view: 'month', 
    focus: null, 
    selected: null, 
    editing: null 
};


(function init() {
    const n = new Date();
    const j = Jalaali.toJ(n.getFullYear(), n.getMonth() + 1, n.getDate());
    state.focus = { ...j };
    state.selected = { ...j };
})();


function keyFrom(j) { 
    return `${j.jy}-${String(j.jm).padStart(2, '0')}-${String(j.jd).padStart(2, '0')}`; 
}

function same(a, b) { 
    return a && b && a.jy === b.jy && a.jm === b.jm && a.jd === b.jd; 
}

function todayJ() {
    const d = new Date();
    return Jalaali.toJ(d.getFullYear(), d.getMonth() + 1, d.getDate());
}

function toPersianNumbers(str) {
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    return str.toString().replace(/[0-9]/g, (digit) => persianDigits[parseInt(digit)]);
}

function toEnglishNumbers(str) {
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    return str.toString().replace(/[۰-۹]/g, (digit) => englishDigits[persianDigits.indexOf(digit)]);
}




async function listByDate(k) {
    try {
        if (!window.dbService) {
            console.warn('Database service not available');
            return [];
        }

        const events = await window.dbService.getEventsByDate(k);
        return events.sort((x, y) => (x.time || '') > (y.time || '') ? 1 : -1);
    } catch (error) {
        console.error('Error fetching events for date:', k, error);
        return [];
    }
}

async function addEvent(k, ev) { 
    try {
        const eventId = await window.dbService.addEvent(k, ev);
        return eventId;
    } catch (error) {

        
        console.error('Error adding event:', error);
        alert('خطا در ذخیره رویداد. لطفاً دوباره تلاش کنید.');
        throw error;
    }
}

async function updateEvent(ok, id, nk, ev) { 
    try {
        await window.dbService.updateEvent(ok, id, nk, ev);
    } catch (error) {
        console.error('Error updating event:', error);
        alert('خطا در بروزرسانی رویداد. لطفاً دوباره تلاش کنید.');
        throw error;
    }
}

async function removeEvent(k, id) { 
    try {
        await window.dbService.deleteEvent(k, id);
    } catch (error) {
        console.error('Error deleting event:', error);
        alert('خطا در حذف رویداد. لطفاً دوباره تلاش کنید.');
        throw error;
    }
}


const grid = document.getElementById('month-grid');
const weekGrid = document.getElementById('week-grid');
const dayList = document.getElementById('day-list');


async function render() {
    document.getElementById('view-month').hidden = state.view !== 'month';
    document.getElementById('view-week').hidden = state.view !== 'week';
    document.getElementById('view-day').hidden = state.view !== 'day';
    
    if (state.view === 'month') await renderMonth();
    if (state.view === 'week') await renderWeek();
    if (state.view === 'day') await renderDay();
    
    syncSelectors();
}


const monthSel = document.getElementById('monthSel');
const yearSel = document.getElementById('yearSel');


MONTHS.forEach((m, i) => {
    const o = document.createElement('option');
    o.value = i + 1;
    o.textContent = m;
    monthSel.appendChild(o);
});


for (let y = 1370; y <= 1450; y++) {
    const o = document.createElement('option');
    o.value = y;
    o.textContent = toPersianNumbers(y);
    yearSel.appendChild(o);
}

function syncSelectors() {
    monthSel.value = state.focus.jm;
    yearSel.value = state.focus.jy;
}


monthSel.addEventListener('change', async () => { 
    state.focus.jm = +monthSel.value; 
    state.focus.jd = Math.min(state.focus.jd, Jalaali.monthLength(state.focus.jy, state.focus.jm)); 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});

yearSel.addEventListener('change', async () => { 
    state.focus.jy = +yearSel.value; 
    state.focus.jd = Math.min(state.focus.jd, Jalaali.monthLength(state.focus.jy, state.focus.jm)); 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});


async function renderMonth() {
    grid.innerHTML = '';
    const jy = state.focus.jy, jm = state.focus.jm;
    const firstJdn = Jalaali.j2d(jy, jm, 1);
    const firstDow = (firstJdn + 2) % 7;
    const startJdn = firstJdn - firstDow;
    
    for (let i = 0; i < 42; i++) {
        const j = Jalaali.d2j(startJdn + i);
        const k = keyFrom(j);
        const cell = document.createElement('button');
        cell.className = 'cell';
        

        if (j.jm !== jm) cell.classList.add('other');
        if ((Jalaali.j2d(j.jy, j.jm, j.jd) + 2) % 7 === 6) cell.classList.add('fri');
        if (same(j, todayJ())) cell.classList.add('today');
        

        const fa = document.createElement('div');
        fa.className = 'num-fa';
        fa.textContent = toPersianNumbers(j.jd);
        cell.appendChild(fa);
        


        

        try {
            const events = await listByDate(k);
            const wrap = document.createElement('div');
            wrap.className = 'ev';
            
            events.slice(0, 2).forEach(e => {
                const s = document.createElement('span');
                s.textContent = (e.time ? e.time + ' · ' : '') + e.title;
                wrap.appendChild(s);
            });
            
            if (events.length > 2) {
                const s = document.createElement('span');
                s.textContent = `+${toPersianNumbers(events.length - 2)}`;
                wrap.appendChild(s);
            }
            
            cell.appendChild(wrap);
        } catch (error) {
            console.error('Error rendering events for date:', k, error);
        }
        

        cell.addEventListener('click', async () => { 
            state.selected = { ...j }; 
            state.view = 'day'; 
            try { 
                await render(); 
            } catch (error) { 
                console.error('Error rendering:', error); 
            }
        });
        
        grid.appendChild(cell);
    }
}


function weekStart(j) {
    const jdn = Jalaali.j2d(j.jy, j.jm, j.jd);
    const dow = (jdn + 2) % 7;
    return Jalaali.d2j(jdn - dow);
}

async function renderWeek() {
    const s = weekStart(state.focus);
    const endWeek = Jalaali.d2j(Jalaali.j2d(s.jy, s.jm, s.jd) + 6);
    document.getElementById('week-title').textContent = ` ${toPersianNumbers(s.jd)} ${MONTHS[s.jm - 1]} تا ${toPersianNumbers(endWeek.jd)} ${MONTHS[endWeek.jm - 1]} ${toPersianNumbers(s.jy)}`;
    weekGrid.innerHTML = '';





    for (let d = 0; d < 7; d++) {
        const j = Jalaali.d2j(Jalaali.j2d(s.jy, s.jm, s.jd) + d);
        const k = keyFrom(j);
        const col = document.createElement('div');
        col.className = 'col';


        const isToday = same(j, todayJ());
        if (isToday) {
            col.style.background = 'linear-gradient(135deg, #fff3e8 0%, #fef8f0 100%)';
            col.style.borderColor = '#ffd9b8';
        }


        const head = document.createElement('div');
        head.className = 'col-header';

        const dayInfo = document.createElement('div');
        dayInfo.innerHTML = `
            <div class="day-name">${WEEKDAYS[d]}</div>
            <div class="day-date">${toPersianNumbers(j.jd)} ${MONTHS[j.jm - 1]}</div>
        `;


        const add = document.createElement('button');
        add.className = 'add-btn';
        add.textContent = '+';
        add.title = 'افزودن رویداد';
        add.addEventListener('click', () => openModal(j));

        head.appendChild(dayInfo);
        head.appendChild(add);
        col.appendChild(head);


        try {
            const events = await listByDate(k);
            events.forEach(ev => {
                const item = document.createElement('div');
                item.className = 'week-event';


                const eventContent = document.createElement('div');
                eventContent.innerHTML = `
                    <div class="event-title">${ev.title}</div>
                    <div class="event-meta">
                        ${ev.time ? `<span class="event-time">${ev.time}</span>` : ''}
                        ${ev.tag ? `<span class="event-tag">${ev.tag}</span>` : ''}
                    </div>
                `;


                const del = document.createElement('button');
                del.className = 'delete-btn';
                del.textContent = 'حذف';
                del.title = 'حذف رویداد';
                del.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    try {
                        await removeEvent(k, ev.id);
                        await render();
                    } catch (error) {
                        console.error('Error deleting event:', error);
                    }
                });

                item.appendChild(eventContent);
                item.appendChild(del);


                item.addEventListener('click', () => openModal(j, ev));
                item.addEventListener('dblclick', async (e) => {
                    e.stopPropagation();
                    if (confirm(`آیا مطمئن هستید که می‌خواهید رویداد "${ev.title}" را حذف کنید؟`)) {
                        try {
                            await removeEvent(k, ev.id);
                            await render();
                        } catch (error) {
                            console.error('Error deleting event:', error);
                        }
                    }
                });
                col.appendChild(item);
            });
        } catch (error) {
            console.error('Error rendering events for date:', k, error);
        }

        weekGrid.appendChild(col);
    }
}


async function renderDay() {
    const j = state.selected;
    document.getElementById('day-title').textContent = `${toPersianNumbers(j.jd)} ${MONTHS[j.jm - 1]} ${toPersianNumbers(j.jy)}`;
    

    const k = keyFrom(j);
    
    try {
        const list = await listByDate(k);
        dayList.innerHTML = '';
        
        if (!list.length) {
            const e = document.createElement('div');
            e.style.color = '#7b8a90';
            e.textContent = 'رویدادی ثبت نشده است';
            dayList.appendChild(e);
            return;
        }
        
        list.forEach(ev => {
            const item = document.createElement('div');
            item.className = 'item';
            item.innerHTML = `<div class="dot"></div><div><div style="font-weight:800">${ev.title}</div><div style="color:#7b8a90;font-size:12px">${ev.time || ''} · ${ev.tag || ''}</div>${ev.notes ? `<div style='color:#60727a;font-size:12px'>${ev.notes}</div>` : ''}</div>`;
            
            // Delete button
            const del = document.createElement('button');
            del.className = 'del';
            del.textContent = 'حذف';
            del.addEventListener('click', async (e) => { 
                e.stopPropagation(); 
                try { 
                    await removeEvent(k, ev.id); 
                    await render(); 
                } catch (error) { 
                    console.error('Error deleting event:', error); 
                }
            });
            item.appendChild(del);
            

            item.addEventListener('click', () => openModal(j, ev));
            dayList.appendChild(item);
        });
    } catch (error) {
        console.error('Error rendering day events:', error);
        dayList.innerHTML = '<div style="color:#bf5b04">خطا در بارگذاری رویدادها</div>';
    }
}


function shiftMonth(d) {
    let { jy, jm, jd } = state.focus;
    jm += d;
    
    while (jm > 12) {
        jm -= 12;
        jy++;
    }
    
    while (jm < 1) {
        jm += 12;
        jy--;
    }
    
    jd = Math.min(jd, Jalaali.monthLength(jy, jm));
    state.focus = { jy, jm, jd };
}


document.getElementById('prev').addEventListener('click', async () => { 
    shiftMonth(1); 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});

document.getElementById('next').addEventListener('click', async () => { 
    shiftMonth(-1); 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});

document.getElementById('today').addEventListener('click', async () => { 
    const t = todayJ(); 
    state.focus = { ...t }; 
    state.selected = { ...t }; 
    state.view = 'month'; 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});

document.getElementById('switchWeek').addEventListener('click', async () => { 
    state.view = 'week'; 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});

document.getElementById('switchDay').addEventListener('click', async () => { 
    state.view = 'day'; 
    state.selected = { ...state.focus }; 
    try { 
        await render(); 
    } catch (error) { 
        console.error('Error rendering:', error); 
    }
});


document.getElementById('addGlobal').addEventListener('click', () => openModal(state.selected || state.focus));
document.getElementById('addDay').addEventListener('click', () => openModal(state.selected || state.focus));


const modal = document.getElementById('modal');

function openModal(j, ev) {
    document.getElementById('modal-title').textContent = ev ? 'ویرایش رویداد' : 'رویداد جدید';
    document.getElementById('deleteEvent').hidden = !ev;
    document.getElementById('event-date').value = toPersianNumbers(`${j.jy}-${String(j.jm).padStart(2, '0')}-${String(j.jd).padStart(2, '0')}`);
    document.getElementById('event-time').value = ev?.time || '';
    document.getElementById('event-title').value = ev?.title || '';
    document.getElementById('event-tag').value = ev?.tag || 'other';
    document.getElementById('event-notes').value = ev?.notes || '';
    document.getElementById('event-id').value = ev?.id || '';
    modal.classList.add('show');
}

function closeModal() {
    modal.classList.remove('show');
}


document.getElementById('closeModal').addEventListener('click', closeModal);

document.getElementById('deleteEvent').addEventListener('click', async () => {
    const id = document.getElementById('event-id').value;
    const key = toEnglishNumbers(document.getElementById('event-date').value.trim());
    
    if (id) {
        try {
            await removeEvent(key, id);
            closeModal();
            await render();
        } catch (error) {
            console.error('Error deleting event:', error);
        }
    }
});


document.getElementById('eventForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const date = toEnglishNumbers(document.getElementById('event-date').value.trim());
    const time = document.getElementById('event-time').value.trim();
    const title = document.getElementById('event-title').value.trim();
    const notes = document.getElementById('event-notes').value.trim();
    const tag = document.getElementById('event-tag').value;
    const id = document.getElementById('event-id').value || null;
    

    const m = date.match(/^(\d{4})-(\d{2})-(\d{2})$/);
    if (!m) {
        alert('فرمت تاریخ نادرست است (مثال: 1404-06-08)');
        return;
    }
    
    const jy = +m[1], jm = +m[2], jd = +m[3];
    

    if (jm < 1 || jm > 12 || jd < 1 || jd > Jalaali.monthLength(jy, jm)) {
        alert('تاریخ نامعتبر است');
        return;
    }
    
    const key = `${jy}-${String(jm).padStart(2, '0')}-${String(jd).padStart(2, '0')}`;
    const originalKey = keyFrom(state.editing || state.selected);

    if (time) {
        try {
            const existingEvents = await listByDate(key);
            const timeConflict = existingEvents.find(event =>
                event.time === time && event.id !== id
            );

            if (timeConflict) {
                alert(`خطا: در ساعت ${time} قبلاً رویداد "${timeConflict.title}" ثبت شده است. لطفاً ساعت دیگری انتخاب کنید.`);
                return;
            }
        } catch (error) {
            console.error('Error checking time conflicts:', error);
        }
    }

    try {
        if (id) {
            await updateEvent(originalKey, id, key, { title, time, tag, notes });
        } else {
            await addEvent(key, { title, time, tag, notes });
        }
        
        state.focus = { jy, jm, jd };
        state.selected = { jy, jm, jd };
        closeModal();
        await render();
    } catch (error) {
        console.error('Error saving event:', error);
    }
});



async function initializeApp() {
    try {
        await new Promise(resolve => {
            const checkDbReady = () => {
                if (window.dbService) {
                    setTimeout(resolve, 100);
                } else {
                    setTimeout(checkDbReady, 50);
                }
            };
            checkDbReady();
        });

        await render();
    } catch (error) {
        console.error('Error in initial render:', error);
    }
}

initializeApp();