
const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');


const app = express();
const PORT = process.env.PORT || 3000;


app.use(cors());
app.use(bodyParser.json());
app.use(express.static('.'))


const dbPath = path.join(__dirname, 'calendar.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('Error opening database:', err);
    } else {
        console.log('Connected to SQLite database');
    }
});


app.get('/api/events/:date', (req, res) => {
    const { date } = req.params;
    
    const query = 'SELECT * FROM events WHERE jalali_date = ? ORDER BY time ASC, created_at ASC';
    
    db.all(query, [date], (err, rows) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        res.json(rows || []);
    });
});


app.get('/api/events', (req, res) => {
    const query = 'SELECT * FROM events ORDER BY jalali_date ASC, time ASC';
    
    db.all(query, [], (err, rows) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        res.json(rows || []);
    });
});


app.post('/api/events', (req, res) => {
    const { jalali_date, title, time, tag, notes } = req.body;
    
    if (!jalali_date || !title) {
        return res.status(400).json({ error: 'Date and title are required' });
    }
    
    const id = crypto.randomUUID();
    const query = `
        INSERT INTO events (id, jalali_date, title, time, tag, notes) 
        VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    db.run(query, [id, jalali_date, title, time || null, tag || null, notes || null], function(err) {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        res.status(201).json({ 
            id, 
            jalali_date, 
            title, 
            time, 
            tag, 
            notes,
            message: 'Event created successfully' 
        });
    });
});


app.put('/api/events/:id', (req, res) => {
    const { id } = req.params;
    const { jalali_date, title, time, tag, notes } = req.body;
    
    if (!jalali_date || !title) {
        return res.status(400).json({ error: 'Date and title are required' });
    }
    
    const query = `
        UPDATE events 
        SET jalali_date = ?, title = ?, time = ?, tag = ?, notes = ? 
        WHERE id = ?
    `;
    
    db.run(query, [jalali_date, title, time || null, tag || null, notes || null, id], function(err) {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        if (this.changes === 0) {
            return res.status(404).json({ error: 'Event not found' });
        }
        
        res.json({ 
            id, 
            jalali_date, 
            title, 
            time, 
            tag, 
            notes,
            message: 'Event updated successfully' 
        });
    });
});


app.delete('/api/events/:id', (req, res) => {
    const { id } = req.params;
    
    const query = 'DELETE FROM events WHERE id = ?';
    
    db.run(query, [id], function(err) {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        if (this.changes === 0) {
            return res.status(404).json({ error: 'Event not found' });
        }
        
        res.json({ message: 'Event deleted successfully' });
    });
});


app.get('/api/events/range/:start/:end', (req, res) => {
    const { start, end } = req.params;
    
    const query = 'SELECT * FROM events WHERE jalali_date BETWEEN ? AND ? ORDER BY jalali_date ASC, time ASC';
    
    db.all(query, [start, end], (err, rows) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        res.json(rows || []);
    });
});


app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});


app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).json({ error: 'Internal server error' });
});


app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log(`Calendar app available at http://localhost:${PORT}/index.html`);
});


process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err);
        } else {
            console.log('Database connection closed');
        }
        process.exit(0);
    });
});
