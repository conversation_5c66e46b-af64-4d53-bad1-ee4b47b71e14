<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تقویم جلالی </title>
    <link rel="stylesheet" href="style.css">
        <script src="database-service.js"></script>
    <script src="script.js" defer></script>
</head>
<body>
    <div class="app">
        <!-- Header Bar -->
        <div class="bar" id="topbar">
            <button class="btn" id="next">ماه قبل ❯</button>
            <div class="spacer"></div>
            <div class="group">
                <select class="drop" id="monthSel" aria-label="ماه"></select>
                <select class="drop" id="yearSel" aria-label="سال"></select>
            </div>
            <div class="spacer"></div>
            <button class="btn" id="prev">❮ ماه بعد</button>
        </div>

        <!-- Weekdays Header -->
        <div class="weekdays">
            <div>شنبه</div>
            <div>یکشنبه</div>
            <div>دوشنبه</div>
            <div>سه‌شنبه</div>
            <div>چهارشنبه</div>
            <div>پنجشنبه</div>
            <div>جمعه</div>
        </div>

        <!-- Month View -->
        <section id="view-month" class="panel month" aria-labelledby="month-title">
            <div class="grid" id="month-grid"></div>
        </section>

        <!-- Week View -->
        <section id="view-week" class="panel week" hidden>
            <div style="padding:8px 12px; font-weight:800" id="week-title"></div>
            <div class="week-grid" id="week-grid"></div>
        </section>

        <!-- Day View -->
        <section id="view-day" class="panel day" hidden>
            <div>
                <div class="big" id="day-title"></div>
                <div style="color:#7b8a90" id="day-sub"></div>
            </div>
            <div>
                <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:6px">
                    <div style="font-weight:800">رویدادهای این روز</div>
                    <button id="addDay" class="btn" style="color:#0f7a6c;background:var(--brand-ink)">+ افزودن</button>
                </div>
                <div class="list" id="day-list"></div>
            </div>
        </section>

        <!-- Control Buttons -->
        <div style="display:flex; gap:8px; justify-content:flex-end; align-items:center; margin-top:10px">
            <button class="btn" id="today" style="color:#0f7a6c;background:var(--brand-ink)">امروز</button>
            <button class="btn" id="switchWeek" style="color:#0f7a6c;background:var(--brand-ink)">نمای هفتگی</button>
            <button class="btn" id="switchDay" style="color:#0f7a6c;background:var(--brand-ink)">نمای روزانه</button>
            <button class="btn" id="addGlobal" style="color:#fff;background:#0f7a6c">+ رویداد</button>
        </div>
    </div>

    <!-- Event Modal -->
    <div class="modal" id="modal">
        <div class="card">
            <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px">
                <div style="font-weight:800" id="modal-title">رویداد جدید</div>
                <button class="btn" id="closeModal">✕</button>
            </div>
            <form id="eventForm" class="form">
                <div class="row">
                    <div>
                        <label>تاریخ (جلالی)</label>
                        <input id="event-date" type="text" placeholder="1404-06-02" required />
                    </div>
                    <div>
                        <label>ساعت</label>
                        <input id="event-time" type="time" />
                    </div>
                </div>
                <div class="row">
                    <div>
                        <label>عنوان</label>
                        <input id="event-title" type="text" required />
                    </div>
                    <div>
                        <label>برچسب</label>
                        <select id="event-tag">
                            <option value="meeting">جلسه</option>
                            <option value="task">کار</option>
                            <option value="personal">شخصی</option>
                            <option value="other">سایر</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label>توضیحات</label>
                    <textarea id="event-notes" placeholder="..."></textarea>
                </div>
                <div style="display:flex; gap:8px; justify-content:flex-end">
                    <button type="button" id="deleteEvent" class="btn" style="background:var(--brand-ink); color:#bf5b04" hidden>حذف</button>
                    <button type="submit" class="btn" style="background:#0f7a6c;color:#fff">ذخیره</button>
                </div>
                <input type="hidden" id="event-id" />
            </form>
        </div>
    </div>

    

</body>
</html>