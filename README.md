# تقویم جلالی با پایگاه داده SQLite

این برنامه تقویم جلالی است که از پایگاه داده SQLite برای ذخیره رویدادها استفاده می‌کند.

## ویژگی‌ها

- تقویم جلالی (هجری شمسی) با نمایش روز/هفته/ماه
- ذخیره مستقیم رویدادها در پایگاه داده SQLite
- رابط کاربری زیبا و کاربرپسند
- پشتیبانی از برچسب‌ها و یادداشت‌ها
- مدیریت خطای پیشرفته
- نشانگر وضعیت اتصال به سرور
- API RESTful برای مدیریت رویدادها

## پیش‌نیازها

- Node.js (نسخه 14 یا بالاتر)
- npm یا yarn

## نصب و راه‌اندازی

### 1. نصب وابستگی‌ها

```bash
npm install
```

### 2. راه‌اندازی پایگاه داده

```bash
node init-db.js
```

این دستور فایل پایگاه داده `calendar.db` را ایجاد می‌کند.

### 3. راه‌اندازی سرور

```bash
npm start
```

یا برای توسعه:

```bash
npm run dev
```

سرور روی پورت 3000 اجرا می‌شود.

### 4. دسترسی به برنامه

مرورگر خود را باز کنید و به آدرس زیر بروید:
```
http://localhost:3000
```

## ساختار پروژه

```
calendar/
├── index.html          # صفحه اصلی برنامه
├── script.js           # منطق اصلی تقویم
├── style.css           # استایل‌های برنامه
├── server.js           # سرور Node.js
├── database-service.js # سرویس پایگاه داده فرانت‌اند
├── init-db.js         # راه‌اندازی پایگاه داده
├── package.json        # وابستگی‌های پروژه
└── calendar.db         # فایل پایگاه داده SQLite
```

## API Endpoints

### رویدادها

- `GET /api/events` - دریافت تمام رویدادها
- `GET /api/events/:date` - دریافت رویدادهای یک تاریخ خاص
- `POST /api/events` - ایجاد رویداد جدید
- `PUT /api/events/:id` - ویرایش رویداد
- `DELETE /api/events/:id` - حذف رویداد
- `GET /api/events/range/:start/:end` - دریافت رویدادها در بازه زمانی

### وضعیت سیستم

- `GET /api/health` - بررسی وضعیت سرور

## ساختار پایگاه داده

جدول `events` شامل فیلدهای زیر است:

- `id` - شناسه یکتا رویداد
- `jalali_date` - تاریخ جلالی (فرمت: YYYY-MM-DD)
- `title` - عنوان رویداد
- `time` - زمان رویداد (اختیاری)
- `tag` - برچسب رویداد (اختیاری)
- `notes` - یادداشت‌ها (اختیاری)
- `created_at` - زمان ایجاد
- `updated_at` - زمان آخرین ویرایش

## ویژگی‌های امنیتی

- استفاده از CORS برای امنیت
- اعتبارسنجی ورودی‌ها
- مدیریت خطاها
- پشتیبانی از HTTPS (در محیط تولید)

## مدیریت خطا و اتصال

برنامه دارای مدیریت خطای پیشرفته است و در صورت عدم دسترسی به سرور، پیام‌های مناسب نمایش می‌دهد. نشانگر وضعیت اتصال در رابط کاربری نمایش داده می‌شود.

## توسعه

برای توسعه برنامه:

1. فایل‌ها را ویرایش کنید
2. سرور را با `npm run dev` اجرا کنید
3. تغییرات به صورت خودکار اعمال می‌شوند

## عیب‌یابی

### مشکلات رایج

1. **خطای اتصال به پایگاه داده**: مطمئن شوید که `init-db.js` اجرا شده است
2. **خطای پورت**: پورت 3000 را بررسی کنید
3. **خطای CORS**: مطمئن شوید که سرور در حال اجرا است

### لاگ‌ها

لاگ‌های سرور در کنسول نمایش داده می‌شوند. برای بررسی خطاها، کنسول سرور را بررسی کنید.

## مشارکت

برای مشارکت در توسعه:

1. پروژه را fork کنید
2. شاخه جدید ایجاد کنید
3. تغییرات را commit کنید
4. Pull Request ارسال کنید

## مجوز

این پروژه تحت مجوز MIT منتشر شده است.
