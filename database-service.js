/**
 * Database Service for Calendar Events
 * Handles all database operations through REST API calls
 */
class DatabaseService {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
        this.isOnline = false;
        this.checkConnection();
    }

    /**
     * Check if backend is available
     */
    async checkConnection() {
        try {
            const response = await fetch(`${this.baseURL}/health`);
            if (response.ok) {
                this.isOnline = true;
                console.log('✅ Backend connection established');
                this.updateConnectionStatus(true);
            } else {
                this.isOnline = false;
                console.warn('⚠️ Backend not responding properly');
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            this.isOnline = false;
            console.warn('❌ Backend not accessible');
            this.updateConnectionStatus(false);
        }
    }

    /**
     * Update connection status in UI
     */
    updateConnectionStatus(isOnline) {
        const statusIndicator = document.getElementById('connection-status');
        if (statusIndicator) {
            statusIndicator.textContent = isOnline ? '🟢 متصل' : '🔴 قطع';
            statusIndicator.style.color = isOnline ? '#0f7a6c' : '#bf5b04';
        }
    }

    /**
     * Get events for a specific date
     */
    async getEventsByDate(jalaliDate) {
        if (!this.isOnline) {
            throw new Error('Backend not available');
        }

        try {
            const response = await fetch(`${this.baseURL}/events/${jalaliDate}`);
            if (response.ok) {
                return await response.json();
            } else {
                console.error('Failed to fetch events:', response.statusText);
                throw new Error('Failed to fetch events');
            }
        } catch (error) {
            console.error('Error fetching events:', error);
            throw error;
        }
    }

    /**
     * Get all events
     */
    async getAllEvents() {
        if (!this.isOnline) {
            throw new Error('Backend not available');
        }

        try {
            const response = await fetch(`${this.baseURL}/events`);
            if (response.ok) {
                return await response.json();
            } else {
                console.error('Failed to fetch all events:', response.statusText);
                throw new Error('Failed to fetch all events');
            }
        } catch (error) {
            console.error('Error fetching all events:', error);
            throw error;
        }
    }

    /**
     * Add new event
     */
    async addEvent(jalaliDate, eventData) {
        if (!this.isOnline) {
            throw new Error('Backend not available');
        }

        try {
            const response = await fetch(`${this.baseURL}/events`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jalali_date: jalaliDate,
                    ...eventData
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Event added successfully:', result);
                return result.id;
            } else {
                console.error('Failed to add event:', response.statusText);
                throw new Error('Failed to add event');
            }
        } catch (error) {
            console.error('Error adding event:', error);
            throw error;
        }
    }

    /**
     * Update event
     */
    async updateEvent(oldDate, eventId, newDate, eventData) {
        if (!this.isOnline) {
            throw new Error('Backend not available');
        }

        try {
            const response = await fetch(`${this.baseURL}/events/${eventId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jalali_date: newDate,
                    ...eventData
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Event updated successfully:', result);
                return true;
            } else {
                console.error('Failed to update event:', response.statusText);
                throw new Error('Failed to update event');
            }
        } catch (error) {
            console.error('Error updating event:', error);
            throw error;
        }
    }

    /**
     * Delete event
     */
    async deleteEvent(jalaliDate, eventId) {
        if (!this.isOnline) {
            throw new Error('Backend not available');
        }

        try {
            const response = await fetch(`${this.baseURL}/events/${eventId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                console.log('Event deleted successfully');
                return true;
            } else {
                console.error('Failed to delete event:', response.statusText);
                throw new Error('Failed to delete event');
            }
        } catch (error) {
            console.error('Error deleting event:', error);
            throw error;
        }
    }

    /**
     * Get events by date range
     */
    async getEventsByRange(startDate, endDate) {
        if (!this.isOnline) {
            throw new Error('Backend not available');
        }

        try {
            const response = await fetch(`${this.baseURL}/events/range/${startDate}/${endDate}`);
            if (response.ok) {
                return await response.json();
            } else {
                console.error('Failed to fetch events by range:', response.statusText);
                throw new Error('Failed to fetch events by range');
            }
        } catch (error) {
            console.error('Error fetching events by range:', error);
            throw error;
        }
    }
}

// Create global instance
window.dbService = new DatabaseService();
